const { OpenAI } = require('openai')
const { Configuration, OpenAIApi } = require('azure-openai')
const url = process.env.OPENAI_ENDPOINT
const key = process.env.OPENAI_KEY
const openai = new OpenAI({
  baseURL: url,
  apiKey: key,
})

const model = process.env.OPENAI_MODEL

const azureOpenAI = new OpenAIApi(
  new Configuration({
    apiKey: key,
    azure: {
      apiKey: key,
      endpoint: url,
      deploymentName: model,
    },
  }),
)

const logging = require('../common/logging')

class OpenAIService {
  async chatCompletion(classification, prompt, maxRetries = 2) {
    let lastError = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        var messages = [
          { role: 'system', content: `${classification}` },
          { role: 'user', content: `${prompt}` },
        ]

        // Adjust max_tokens based on prompt length for very long conversations
        let maxTokens = 20000
        // if (prompt.length > 10000) {
        //   maxTokens = 6000 // Increase for very long conversations
        // } else if (prompt.length > 5000) {
        //   maxTokens = 5000 // Moderate increase for long conversations
        // }

        var result = await azureOpenAI.createChatCompletion({
          messages: messages,
          temperature: 0,
          top_p: 0,
          max_tokens: maxTokens,
        })

        var data = result.data.choices[0].message.content

        // Validate that we got a reasonable response
        if (!data || data.trim().length === 0) {
          throw new Error('Empty response from OpenAI')
        }

        return data
      } catch (error) {
        lastError = error
        logging.logError(
          `OpenAI chat completion attempt ${attempt + 1} failed`,
          error,
        )

        // If this is not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * (attempt + 1)),
          ) // Exponential backoff
        }
      }
    }

    logging.logError(`All OpenAI chat completion attempts failed`, lastError)
    return null
  }
}

module.exports = new OpenAIService()
